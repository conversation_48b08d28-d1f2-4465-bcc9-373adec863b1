"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/ProjectsDashboardContent.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsDashboardContent: () => (/* binding */ ProjectsDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useProjectsDashboard */ \"(app-pages-browser)/./src/hooks/useProjectsDashboard.ts\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/LabelList.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Label.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectsDashboardContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard)();\n    // Chart configurations with gold theme\n    const statusChartConfig = {\n        not_started: {\n            label: 'Belum Dimulai',\n            theme: {\n                light: '#D1D5DB',\n                dark: '#D1D5DB'\n            },\n            fill: '#D1D5DB'\n        },\n        in_progress: {\n            label: 'Dalam Proses',\n            theme: {\n                light: '#B78F38',\n                dark: '#B78F38'\n            },\n            fill: '#B78F38'\n        },\n        completed: {\n            label: 'Selesai',\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        cancelled: {\n            label: 'Dibatalkan',\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        }\n    };\n    const categoryChartConfig = {\n        category1: {\n            theme: {\n                light: '#B78F38',\n                dark: '#B78F38'\n            },\n            fill: '#B78F38'\n        },\n        category2: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        category3: {\n            theme: {\n                light: '#D4AF37',\n                dark: '#D4AF37'\n            },\n            fill: '#D4AF37'\n        },\n        category4: {\n            theme: {\n                light: '#F4E4BC',\n                dark: '#F4E4BC'\n            },\n            fill: '#F4E4BC'\n        },\n        category5: {\n            theme: {\n                light: '#8B7355',\n                dark: '#8B7355'\n            },\n            fill: '#8B7355'\n        }\n    };\n    const picChartConfig = {\n        pic: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        label: {\n            color: 'hsl(var(--background))'\n        }\n    };\n    const kpiChartConfig = {\n        not_started: {\n            theme: {\n                light: '#D1D5DB',\n                dark: '#D1D5DB'\n            },\n            fill: '#D1D5DB'\n        },\n        in_progress: {\n            theme: {\n                light: '#B78F38',\n                dark: '#B78F38'\n            },\n            fill: '#B78F38'\n        },\n        completed_below_target: {\n            theme: {\n                light: '#F59E0B',\n                dark: '#F59E0B'\n            },\n            fill: '#F59E0B'\n        },\n        completed_on_target: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        completed_above_target: {\n            theme: {\n                light: '#D4AF37',\n                dark: '#D4AF37'\n            },\n            fill: '#D4AF37'\n        }\n    };\n    // Function to get status badge color with gold theme\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n                return 'bg-gray-100 text-gray-700 border border-gray-300';\n            case 'in progress':\n            case 'in_progress':\n                return 'bg-amber-50 text-amber-800 border border-amber-200';\n            case 'completed':\n                return 'bg-yellow-50 text-yellow-800 border border-yellow-200';\n            case 'cancelled':\n                return 'bg-gray-100 text-gray-600 border border-gray-300';\n            default:\n                return 'bg-gray-100 text-gray-700 border border-gray-300';\n        }\n    };\n    // Function to get KPI status color and fill with gold theme\n    const getKpiStatusColor = function(status) {\n        let forChart = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        switch(status.toLowerCase()){\n            case 'not_started':\n                return forChart ? '#D1D5DB' : 'bg-gray-400';\n            case 'in_progress':\n                return forChart ? '#B78F38' : 'bg-amber-600';\n            case 'completed_below_target':\n                return forChart ? '#F59E0B' : 'bg-yellow-500';\n            case 'completed_on_target':\n                return forChart ? '#AB8B3B' : 'bg-yellow-600';\n            case 'completed_above_target':\n                return forChart ? '#D4AF37' : 'bg-yellow-500';\n            default:\n                return forChart ? '#B78F38' : 'bg-amber-600';\n        }\n    };\n    // Function to get chart fill color for project status with gold theme\n    const getStatusChartColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n            case 'belum dimulai':\n                return '#D1D5DB'; // Light gray\n            case 'in progress':\n            case 'in_progress':\n            case 'dalam proses':\n                return '#B78F38'; // Primary gold\n            case 'completed':\n            case 'selesai':\n                return '#AB8B3B'; // Darker gold\n            case 'cancelled':\n            case 'dibatalkan':\n                return '#9CA3AF'; // Gray\n            default:\n                return '#D1D5DB'; // Light gray\n        }\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-32 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-red-50 border-red-200 mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Coba Lagi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare data for charts\n    const statusData = Object.entries(dashboardData.projects.by_status).map((param)=>{\n        let [key, value] = param;\n        const colorKey = key.toLowerCase().replace(/ /g, '_');\n        return {\n            name: key,\n            value,\n            fill: \"var(--color-\".concat(colorKey, \")\")\n        };\n    });\n    // Create an array of colors for categories\n    const categoryColors = [\n        'hsl(var(--chart-1))',\n        'hsl(var(--chart-2))',\n        'hsl(var(--chart-3))',\n        'hsl(var(--chart-4))',\n        'hsl(var(--chart-5))'\n    ];\n    const categoryData = Object.entries(dashboardData.projects.by_category).map((param, index)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: categoryColors[index % categoryColors.length]\n        };\n    });\n    const picData = dashboardData.projects.by_pic.map((pic)=>({\n            name: pic.name,\n            value: pic.count,\n            fill: '#AB8B3B'\n        }));\n    // Prepare data for KPI donut chart\n    const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n            value,\n            fill: getKpiStatusColor(key, true)\n        };\n    });\n    // Calculate total KPI achievement for donut chart center\n    const totalKpiAchievement = dashboardData.kpis.achievement_percentage;\n    // Filter upcoming deadlines to only show those within the next month (30 days)\n    const upcomingDeadlinesNextMonth = dashboardData.projects.upcoming_deadlines.filter((project)=>project.days_remaining <= 30);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-4 px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                title: \"Dashboard Proyek\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-gray-600 text-sm\",\n                children: \"Ringkasan statistik untuk semua proyek.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-l-4 border-l-amber-500 bg-gradient-to-r from-amber-50 to-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2 text-amber-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Proyek\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-amber-900\",\n                                    children: dashboardData.projects.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-l-4 border-l-yellow-500 bg-gradient-to-r from-yellow-50 to-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2 text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total KPI\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-yellow-900\",\n                                        children: dashboardData.kpis.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-yellow-700 mt-1 bg-yellow-100 px-2 py-1 rounded-md inline-block\",\n                                        children: [\n                                            \"Pencapaian: \",\n                                            dashboardData.kpis.achievement_percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-l-4 border-l-amber-600 bg-gradient-to-r from-amber-50 to-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2 text-amber-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Tugas\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-amber-900\",\n                                        children: dashboardData.tasks.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-amber-700 mt-1 bg-amber-100 px-2 py-1 rounded-md inline-block\",\n                                        children: [\n                                            \"Selesai: \",\n                                            Math.round(dashboardData.tasks.completed / dashboardData.tasks.total * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-l-4 border-l-orange-500 bg-gradient-to-r from-orange-50 to-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2 text-orange-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Tenggat Waktu\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-orange-900\",\n                                        children: upcomingDeadlinesNextMonth.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-orange-600 mt-1\",\n                                        children: \"Dalam 30 hari mendatang\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-2 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-amber-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-base text-amber-800\",\n                                    children: \"Proyek berdasarkan Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: statusChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                    data: statusData,\n                                                    dataKey: \"value\",\n                                                    nameKey: \"name\",\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    outerRadius: 70,\n                                                    paddingAngle: 2,\n                                                    label: true,\n                                                    children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: getStatusChartColor(entry.name)\n                                                        }, \"status-\".concat(entry.name, \"-\").concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegend, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegendContent, {\n                                                        nameKey: \"name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-amber-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-base text-amber-800\",\n                                    children: \"Proyek berdasarkan Kategori\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: categoryChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: categoryData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 30,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    width: 120\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    children: categoryData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: categoryColors[index % categoryColors.length]\n                                                        }, \"category-\".concat(entry.name, \"-\").concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-2 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"border-amber-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-base text-amber-800\",\n                                    children: \"Proyek berdasarkan PIC\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: picChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: picData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 50,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    cursor: false,\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    fill: \"#AB8B3B\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"name\",\n                                                            position: \"insideLeft\",\n                                                            offset: 8,\n                                                            className: \"fill-[--color-label]\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"value\",\n                                                            position: \"right\",\n                                                            offset: 8,\n                                                            className: \"fill-foreground\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Status KPI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"flex-1 pb-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                    config: kpiChartConfig,\n                                    className: \"mx-auto aspect-square max-h-[250px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                cursor: false,\n                                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {\n                                                    hideLabel: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 28\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                data: kpiStatusData,\n                                                dataKey: \"value\",\n                                                nameKey: \"name\",\n                                                innerRadius: 60,\n                                                outerRadius: 80,\n                                                paddingAngle: 2,\n                                                children: [\n                                                    kpiStatusData.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: entry.fill\n                                                        }, \"cell-\".concat(entry.name), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                        content: (param)=>{\n                                                            let { viewBox } = param;\n                                                            if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                    x: viewBox.cx,\n                                                                    y: viewBox.cy,\n                                                                    textAnchor: \"middle\",\n                                                                    dominantBaseline: \"middle\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: viewBox.cy,\n                                                                            className: \"fill-foreground text-3xl font-bold\",\n                                                                            children: [\n                                                                                totalKpiAchievement,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 534,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: (viewBox.cy || 0) + 24,\n                                                                            className: \"fill-muted-foreground text-sm\",\n                                                                            children: \"Pencapaian\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 29\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 27\n                                                                }, void 0);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-col gap-2 text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 font-medium leading-none justify-center\",\n                                        children: dashboardData.kpis.achievement_percentage >= 100 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian melebihi target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : dashboardData.kpis.achievement_percentage >= 80 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian mendekati target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian perlu ditingkatkan\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 437,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek Terbaru\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: dashboardData.projects.recent.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: project.project_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: project.organization_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(project.status_project),\n                                                            children: project.status_project\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"PIC: \",\n                                                                project.pic_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: project.days_remaining > 0 ? \"\".concat(project.days_remaining, \" hari tersisa\") : 'Tenggat waktu terlewati'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 585,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Tenggat Waktu Mendatang (30 Hari)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upcomingDeadlinesNextMonth.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: project.project_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: project.days_remaining <= 7 ? 'border-red-500 text-red-700' : project.days_remaining <= 30 ? 'border-yellow-500 text-yellow-700' : 'border-green-500 text-green-700',\n                                                            children: [\n                                                                project.days_remaining,\n                                                                \" hari\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Tenggat: \",\n                                                                (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_7__.formatDate)(project.end_project)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Progres: \",\n                                                                project.progress_percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 583,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsDashboardContent, \"dYLgM1N2lllAS4iOBb0m1Po550g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard\n    ];\n});\n_c = ProjectsDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectsDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx\n"));

/***/ })

});