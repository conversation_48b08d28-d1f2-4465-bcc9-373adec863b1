"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx":
/*!************************************************************!*\
  !*** ./src/components/project/ProjectDashboardContent.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDashboardContent: () => (/* binding */ ProjectDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/scroll.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,Calendar,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/BackButton */ \"(app-pages-browser)/./src/components/ui/BackButton.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_format__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/format */ \"(app-pages-browser)/./src/lib/utils/format.ts\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useProjectDashboard */ \"(app-pages-browser)/./src/hooks/useProjectDashboard.ts\");\n/* harmony import */ var _lib_api_project__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/api/project */ \"(app-pages-browser)/./src/lib/api/project.ts\");\n/* harmony import */ var _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useRBAC */ \"(app-pages-browser)/./src/hooks/useRBAC.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectDashboardContent(param) {\n    let { id } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { hasRole } = (0,_hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC)();\n    const canEdit = hasRole([\n        'Operation',\n        'Manager'\n    ]);\n    const canDelete = hasRole([\n        'Manager'\n    ]);\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(id);\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteLoading, setDeleteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch full project data to get project_charter_id\n    const fetchProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProjectDashboardContent.useCallback[fetchProject]\": async ()=>{\n            if (!id) return;\n            try {\n                const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.getProjectById(id);\n                if (response.success && response.data) {\n                    setProject(response.data);\n                } else {\n                    console.error(\"Failed to fetch project: \".concat(response.message));\n                }\n            } catch (error) {\n                console.error('Error fetching project:', error);\n            }\n        }\n    }[\"ProjectDashboardContent.useCallback[fetchProject]\"], [\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDashboardContent.useEffect\": ()=>{\n            fetchProject();\n        }\n    }[\"ProjectDashboardContent.useEffect\"], [\n        fetchProject\n    ]);\n    // Handle project deletion\n    const handleDelete = async ()=>{\n        if (!canDelete) {\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Anda tidak memiliki izin untuk menghapus proyek');\n            setDeleteDialogOpen(false);\n            return;\n        }\n        try {\n            setDeleteLoading(true);\n            const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.deleteProject(id);\n            if (response.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Proyek berhasil dihapus');\n                router.push('/project');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Gagal menghapus proyek: \".concat(response.message));\n            }\n        } catch (error) {\n            console.error('Error deleting project:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Terjadi kesalahan saat menghapus proyek');\n        } finally{\n            setDeleteLoading(false);\n            setDeleteDialogOpen(false);\n        }\n    };\n    // Function to get status badge color\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'Not Started':\n                return 'bg-gray-200 text-gray-800';\n            case 'In Progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'Completed':\n                return 'bg-green-100 text-green-800';\n            case 'Cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to get KPI status badge color\n    const getKpiStatusColor = (status)=>{\n        switch(status){\n            case 'not_started':\n                return 'bg-gray-200 text-gray-800';\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed_below_target':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'completed_on_target':\n                return 'bg-green-100 text-green-800';\n            case 'completed_above_target':\n                return 'bg-emerald-100 text-emerald-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to get task status badge color\n    const getTaskStatusColor = (status)=>{\n        switch(status){\n            case 'not_completed':\n                return 'bg-red-100 text-red-800';\n            case 'on_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to format KPI status for display\n    const formatKpiStatus = (status)=>{\n        switch(status){\n            case 'not_started':\n                return 'Belum Dimulai';\n            case 'in_progress':\n                return 'Dalam Proses';\n            case 'completed_below_target':\n                return 'Selesai Di Bawah Target';\n            case 'completed_on_target':\n                return 'Selesai Sesuai Target';\n            case 'completed_above_target':\n                return 'Selesai Di Atas Target';\n            default:\n                return status;\n        }\n    };\n    // Function to format task status for display\n    const formatTaskStatus = (status)=>{\n        switch(status){\n            case 'not_completed':\n                return 'Belum Selesai';\n            case 'on_progress':\n                return 'Dalam Proses';\n            case 'completed':\n                return 'Selesai';\n            default:\n                return status;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push(\"/project\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                        className: \"h-8 w-64\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-4 md:grid-cols-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                className: \"h-24 w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                className: \"h-24 w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                    className: \"h-64 w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                    className: \"h-64 w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                    className: \"h-64 w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push('/project')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"bg-red-50 border-red-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: \"Coba Lagi\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 212,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                                onClick: ()=>router.push(\"/project\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                                title: \"Dashboard Proyek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canEdit && (!project.project_charter_id || project.project_charter_id === 'TODO-project-charter-implementation') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter/create\")),\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Tambah Project Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this),\n                                    project.project_charter_id && project.project_charter_id !== 'TODO-project-charter-implementation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter\")),\n                                        variant: \"outline\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Project Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/detail\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Detail Proyek\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/gantt\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Gantt Chart\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Weekly Log\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Lihat KPI\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Tugas Proyek\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"destructive\",\n                                onClick: ()=>setDeleteDialogOpen(true),\n                                children: \"Hapus\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: dashboardData.project_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: dashboardData.organization_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: getStatusColor(dashboardData.status_project),\n                                            children: dashboardData.status_project\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            variant: \"outline\",\n                                            children: dashboardData.project_category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-500 mb-2\",\n                                            children: \"Tujuan Proyek\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: dashboardData.objectives\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-500 mb-2\",\n                                            children: \"Anggaran\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: (0,_lib_utils_format__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(parseInt(dashboardData.budget_project))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"border-amber-200 bg-gradient-to-br from-amber-50 to-yellow-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-amber-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-5 w-5 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Progres Proyek\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-amber-800\",\n                                                        children: \"Penyelesaian\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-amber-900\",\n                                                        children: [\n                                                            dashboardData.progress_percentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-amber-200 rounded-full h-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-amber-500 to-yellow-500 h-3 rounded-full transition-all duration-500\",\n                                                    style: {\n                                                        width: \"\".concat(dashboardData.progress_percentage, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-3 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center bg-amber-100 rounded-lg p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-amber-600\",\n                                                        children: \"Hari Berlalu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-bold text-amber-800\",\n                                                        children: dashboardData.days_elapsed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center bg-yellow-100 rounded-lg p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-600\",\n                                                        children: \"Hari Tersisa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-bold text-yellow-800\",\n                                                        children: dashboardData.days_remaining\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center bg-orange-100 rounded-lg p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-orange-600\",\n                                                        children: \"Total Hari\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-bold text-orange-800\",\n                                                        children: dashboardData.days_total\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"border-yellow-200 bg-gradient-to-br from-yellow-50 to-amber-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Timeline Proyek\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-100 rounded-lg p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-yellow-600\",\n                                                            children: \"Tanggal Mulai\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-yellow-800\",\n                                                            children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.start_project)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right bg-amber-100 rounded-lg p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-amber-600\",\n                                                            children: \"Tanggal Selesai\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.end_project)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-yellow-200 h-2 rounded-full absolute top-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-yellow-500 to-amber-500 h-2 rounded-full absolute top-0 transition-all duration-500\",\n                                                    style: {\n                                                        width: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-amber-600 rounded-full absolute top-[-4px] border-2 border-white shadow-md\",\n                                                    style: {\n                                                        left: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\"),\n                                                        transform: 'translateX(-50%)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"border-yellow-200 bg-gradient-to-br from-yellow-50 to-amber-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Status KPI (\",\n                                                dashboardData.kpi_count,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: getKpiStatusColor(dashboardData.kpi_status),\n                                            children: formatKpiStatus(dashboardData.kpi_status)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 13\n                                    }, this),\n                                    dashboardData.kpis.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: dashboardData.kpis.map((kpi)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-yellow-200 rounded-lg p-3 bg-yellow-50/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-yellow-900\",\n                                                        children: kpi.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-yellow-700 mt-1\",\n                                                        children: [\n                                                            \"Target: \",\n                                                            kpi.target\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-yellow-600\",\n                                                                children: kpi.period\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: getKpiStatusColor(kpi.status),\n                                                                children: formatKpiStatus(kpi.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, kpi.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-600 text-sm\",\n                                        children: \"Tidak ada KPI yang tersedia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full border-yellow-300 text-yellow-700 hover:bg-yellow-100\",\n                                            onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                            children: \"Lihat Semua KPI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"border-amber-200 bg-gradient-to-br from-amber-50 to-yellow-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-amber-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-5 w-5 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Tugas (\",\n                                                dashboardData.tasks_total,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-amber-50 rounded-lg p-2 text-center border border-amber-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-amber-600\",\n                                                        children: \"Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-bold text-amber-800\",\n                                                        children: dashboardData.tasks_completed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 rounded-lg p-2 text-center border border-yellow-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-600\",\n                                                        children: \"Proses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-bold text-yellow-800\",\n                                                        children: dashboardData.tasks_in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 rounded-lg p-2 text-center border border-orange-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-orange-600\",\n                                                        children: \"Belum\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-bold text-orange-800\",\n                                                        children: dashboardData.tasks_not_started\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-gradient-to-r from-amber-50 to-yellow-50 p-3 rounded-lg border border-amber-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-amber-800\",\n                                                        children: \"Persentase Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-amber-900\",\n                                                        children: [\n                                                            Math.round(dashboardData.tasks_completed / dashboardData.tasks_total * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-amber-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-amber-500 to-yellow-500 h-2 rounded-full transition-all duration-300\",\n                                                    style: {\n                                                        width: \"\".concat(Math.round(dashboardData.tasks_completed / dashboardData.tasks_total * 100), \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 13\n                                    }, this),\n                                    dashboardData.recent_tasks.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: dashboardData.recent_tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-sm\",\n                                                        children: task.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: task.employee_name || 'Tidak ada assignee'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: getTaskStatusColor(task.completion_status),\n                                                                children: formatTaskStatus(task.completion_status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            \"Tenggat: \",\n                                                            (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(task.due_date)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, task.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: \"Tidak ada tugas yang tersedia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full\",\n                                            onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                            children: \"Lihat Semua Tugas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_Calendar_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Log Mingguan (\",\n                                                dashboardData.weekly_logs_count,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: [\n                                    dashboardData.recent_weekly_logs.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: dashboardData.recent_weekly_logs.slice(0, 5).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Minggu #\",\n                                                                    log.week_number\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs\",\n                                                                children: [\n                                                                    log.notes_count,\n                                                                    \" catatan\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(log.week_start_date),\n                                                            \" -\",\n                                                            ' ',\n                                                            (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(log.week_end_date)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, log.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: \"Tidak ada log mingguan yang tersedia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full\",\n                                            onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                            children: \"Lihat Semua Log\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogTitle, {\n                                    children: \"Hapus Proyek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogDescription, {\n                                    children: \"Apakah Anda yakin ingin menghapus proyek ini? Tindakan ini tidak dapat dibatalkan.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    disabled: deleteLoading,\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDelete,\n                                    disabled: deleteLoading,\n                                    children: deleteLoading ? 'Menghapus...' : 'Hapus'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 644,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDashboardContent, \"8ZU5MIOROIm4/loS7tGVBmfEBL4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC,\n        _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    ];\n});\n_c = ProjectDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx\n"));

/***/ })

});