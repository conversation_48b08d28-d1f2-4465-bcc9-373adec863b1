"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx":
/*!************************************************************!*\
  !*** ./src/components/project/ProjectDashboardContent.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDashboardContent: () => (/* binding */ ProjectDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/scroll.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CalendarDays,CheckCircle,CheckSquare,Eye,FileText,Plus,Scroll,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/BackButton */ \"(app-pages-browser)/./src/components/ui/BackButton.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_format__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/format */ \"(app-pages-browser)/./src/lib/utils/format.ts\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useProjectDashboard */ \"(app-pages-browser)/./src/hooks/useProjectDashboard.ts\");\n/* harmony import */ var _lib_api_project__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/api/project */ \"(app-pages-browser)/./src/lib/api/project.ts\");\n/* harmony import */ var _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useRBAC */ \"(app-pages-browser)/./src/hooks/useRBAC.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// --- Helper: Gold/White palette\nconst palette = {\n    bgMain: 'bg-[#fffbea]',\n    bgCard: 'bg-white',\n    gold: 'text-[#dfb14a]',\n    goldBg: 'bg-[#fff2ce]',\n    goldAccent: 'bg-[#f7e3a1]',\n    border: 'border-[#ecd9a0]',\n    progress: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',\n    green: 'text-emerald-700 bg-emerald-100',\n    blue: 'text-blue-700 bg-blue-100',\n    red: 'text-red-700 bg-red-100',\n    gray: 'text-gray-700 bg-gray-100'\n};\nfunction ProjectDashboardContent(param) {\n    let { id } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { hasRole } = (0,_hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC)();\n    const canEdit = hasRole([\n        'Operation',\n        'Manager'\n    ]);\n    const canDelete = hasRole([\n        'Manager'\n    ]);\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(id);\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteLoading, setDeleteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch full project data to get project_charter_id\n    const fetchProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProjectDashboardContent.useCallback[fetchProject]\": async ()=>{\n            if (!id) return;\n            try {\n                const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.getProjectById(id);\n                if (response.success && response.data) {\n                    setProject(response.data);\n                } else {\n                    console.error(\"Failed to fetch project: \".concat(response.message));\n                }\n            } catch (error) {\n                console.error('Error fetching project:', error);\n            }\n        }\n    }[\"ProjectDashboardContent.useCallback[fetchProject]\"], [\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectDashboardContent.useEffect\": ()=>{\n            fetchProject();\n        }\n    }[\"ProjectDashboardContent.useEffect\"], [\n        fetchProject\n    ]);\n    // Handle project deletion\n    const handleDelete = async ()=>{\n        if (!canDelete) {\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Anda tidak memiliki izin untuk menghapus proyek');\n            setDeleteDialogOpen(false);\n            return;\n        }\n        try {\n            setDeleteLoading(true);\n            const response = await _lib_api_project__WEBPACK_IMPORTED_MODULE_13__.projectApi.deleteProject(id);\n            if (response.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Proyek berhasil dihapus');\n                router.push('/project');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Gagal menghapus proyek: \".concat(response.message));\n            }\n        } catch (error) {\n            console.error('Error deleting project:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Terjadi kesalahan saat menghapus proyek');\n        } finally{\n            setDeleteLoading(false);\n            setDeleteDialogOpen(false);\n        }\n    };\n    // --- Status helpers\n    const statusColors = {\n        'Not Started': palette.gray,\n        'In Progress': palette.blue,\n        'Completed': palette.green,\n        'Cancelled': palette.red\n    };\n    const getStatusColor = (status)=>statusColors[status] || palette.gray;\n    const kpiColors = {\n        'not_started': palette.gray,\n        'in_progress': palette.blue,\n        'completed_below_target': 'bg-yellow-100 text-yellow-800',\n        'completed_on_target': palette.green,\n        'completed_above_target': 'bg-[#e7f8e5] text-[#107c41]'\n    };\n    const getKpiStatusColor = (status)=>kpiColors[status] || palette.gray;\n    const taskColors = {\n        'not_completed': palette.red,\n        'on_progress': palette.blue,\n        'completed': palette.green\n    };\n    const getTaskStatusColor = (status)=>taskColors[status] || palette.gray;\n    // Format status\n    const formatKpiStatus = (status)=>({\n            'not_started': 'Belum Dimulai',\n            'in_progress': 'Dalam Proses',\n            'completed_below_target': 'Selesai Di Bawah Target',\n            'completed_on_target': 'Selesai Sesuai Target',\n            'completed_above_target': 'Selesai Di Atas Target'\n        })[status] || status;\n    const formatTaskStatus = (status)=>({\n            'not_completed': 'Belum Selesai',\n            'on_progress': 'Dalam Proses',\n            'completed': 'Selesai'\n        })[status] || status;\n    // Calculate task completion percent\n    let taskPercent = 0;\n    let kpiPercent = 0;\n    if (dashboardData && dashboardData.tasks_total > 0) {\n        taskPercent = Math.round(dashboardData.tasks_completed / dashboardData.tasks_total * 100);\n    }\n    if (dashboardData && dashboardData.kpi_count > 0 && dashboardData.kpis) {\n        const completedKpis = dashboardData.kpis.filter((kpi)=>kpi.status === 'completed_on_target' || kpi.status === 'completed_above_target').length;\n        kpiPercent = Math.round(completedKpis / dashboardData.kpi_count * 100);\n    }\n    // --- Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-4 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push(\"/project\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 lg:grid-cols-3 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-40 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-56 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-56 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: \"h-56 w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(palette.bgMain, \" min-h-screen py-4 px-2\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                            onClick: ()=>router.push('/project')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                            title: \"Dashboard Proyek\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border \".concat(palette.border, \" \").concat(palette.goldBg),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: \"Coba Lagi\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    // --- Main dashboard\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(palette.bgMain, \" min-h-screen py-4 px-2\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackButton__WEBPACK_IMPORTED_MODULE_8__.BackButton, {\n                                onClick: ()=>router.push(\"/project\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_7__.PageTitle, {\n                                title: \"Dashboard Proyek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 flex-wrap\",\n                        children: [\n                            project && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canEdit && (!project.project_charter_id || project.project_charter_id === 'TODO-project-charter-implementation') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter/create\")),\n                                        className: \"flex items-center gap-2 \".concat(palette.goldBg, \" border \").concat(palette.border, \" \").concat(palette.gold),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Tambah Project Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 19\n                                    }, this),\n                                    project.project_charter_id && project.project_charter_id !== 'TODO-project-charter-implementation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/project/\".concat(project.id, \"/charter\")),\n                                        variant: \"outline\",\n                                        className: \"flex items-center gap-2 \".concat(palette.gold),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Project Charter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/detail\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Detail Proyek\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/gantt\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Gantt Chart\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Weekly Log\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Lihat KPI\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                className: \"flex items-center gap-2\",\n                                onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Tugas Proyek\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"destructive\",\n                                onClick: ()=>setDeleteDialogOpen(true),\n                                children: \"Hapus\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-3 gap-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold\",\n                                            children: dashboardData.project_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"\".concat(palette.gold, \" text-base\"),\n                                            children: dashboardData.organization_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    className: getStatusColor(dashboardData.status_project),\n                                                    children: dashboardData.status_project\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: \"outline\",\n                                                    children: dashboardData.project_category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-700\",\n                                                    children: \"Tujuan:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: dashboardData.objectives\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-700\",\n                                                    children: \"Anggaran:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: (0,_lib_utils_format__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(parseInt(dashboardData.budget_project))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"col-span-2 \".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 mb-1\",\n                                                children: \"Progres Proyek\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center h-24 w-24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"absolute\",\n                                                        width: \"96\",\n                                                        height: \"96\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"48\",\n                                                                cy: \"48\",\n                                                                r: \"40\",\n                                                                stroke: \"#f7e3a1\",\n                                                                strokeWidth: \"12\",\n                                                                fill: \"none\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"48\",\n                                                                cy: \"48\",\n                                                                r: \"40\",\n                                                                stroke: \"#dfb14a\",\n                                                                strokeWidth: \"12\",\n                                                                fill: \"none\",\n                                                                strokeDasharray: 2 * Math.PI * 40,\n                                                                strokeDashoffset: 2 * Math.PI * 40 * (1 - dashboardData.progress_percentage / 100),\n                                                                strokeLinecap: \"round\",\n                                                                style: {\n                                                                    transition: 'stroke-dashoffset 0.5s'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"\".concat(palette.gold, \" absolute font-bold text-2xl\"),\n                                                        children: [\n                                                            dashboardData.progress_percentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 text-xs mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"Hari Berlalu\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: dashboardData.days_elapsed\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"Tersisa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: dashboardData.days_remaining\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"Total\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: dashboardData.days_total\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 mb-1\",\n                                                children: \"Timeline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-6 mb-2 w-full justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Mulai\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.start_project)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Selesai\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_11__.formatDate)(dashboardData.end_project)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-3 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute w-full h-3 rounded-full\",\n                                                        style: {\n                                                            background: '#fff2ce'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute h-3 rounded-full\",\n                                                        style: {\n                                                            background: 'linear-gradient(90deg,#ffe18f,#dfb14a)',\n                                                            width: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-0 w-4 h-4 rounded-full border-2 border-white\",\n                                                        style: {\n                                                            background: '#dfb14a',\n                                                            left: \"\".concat(dashboardData.days_elapsed / dashboardData.days_total * 100, \"%\"),\n                                                            transform: 'translate(-50%,-25%)'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-3 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#dfb14a]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                dashboardData.kpi_count,\n                                                \" KPI\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center h-16 w-16\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"absolute\",\n                                                        width: \"64\",\n                                                        height: \"64\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"32\",\n                                                                cy: \"32\",\n                                                                r: \"26\",\n                                                                stroke: \"#f7e3a1\",\n                                                                strokeWidth: \"8\",\n                                                                fill: \"none\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"32\",\n                                                                cy: \"32\",\n                                                                r: \"26\",\n                                                                stroke: \"#dfb14a\",\n                                                                strokeWidth: \"8\",\n                                                                fill: \"none\",\n                                                                strokeDasharray: 2 * Math.PI * 26,\n                                                                strokeDashoffset: 2 * Math.PI * 26 * (1 - kpiPercent / 100),\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute text-lg font-bold text-[#dfb14a]\",\n                                                        children: [\n                                                            kpiPercent,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: [\n                                                    \"KPI Tercapai (\",\n                                                    kpiPercent,\n                                                    \"%)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-3 flex flex-col gap-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: getKpiStatusColor(dashboardData.kpi_status),\n                                            children: formatKpiStatus(dashboardData.kpi_status)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            dashboardData.kpis.slice(0, 2).map((kpi)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between border-b last:border-none pb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-xs\",\n                                                                children: kpi.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            className: getKpiStatusColor(kpi.status),\n                                                            children: formatKpiStatus(kpi.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, kpi.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            dashboardData.kpis.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"Tidak ada KPI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full mt-3\",\n                                        onClick: ()=>router.push(\"/kpi-project/with-details?projectId=\".concat(id)),\n                                        children: \"Lihat Semua KPI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#dfb14a]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                dashboardData.tasks_total,\n                                                \" Tugas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-4 rounded-full bg-[#fff2ce] relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 rounded-full\",\n                                                        style: {\n                                                            width: \"\".concat(taskPercent, \"%\"),\n                                                            background: 'linear-gradient(90deg,#ffe18f,#dfb14a)'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute right-2 top-0.5 text-xs font-bold text-[#dfb14a]\",\n                                                        children: [\n                                                            taskPercent,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: [\n                                                    \"Penyelesaian Tugas (\",\n                                                    taskPercent,\n                                                    \"%)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs mt-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-emerald-700\",\n                                                        children: dashboardData.tasks_completed\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-gray-500\",\n                                                        children: \"Selesai\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-blue-700\",\n                                                        children: dashboardData.tasks_in_progress\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-gray-500\",\n                                                        children: \"Proses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-red-700\",\n                                                        children: dashboardData.tasks_not_started\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-gray-500\",\n                                                        children: \"Belum\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            dashboardData.recent_tasks.slice(0, 2).map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between border-b last:border-none pb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium\",\n                                                            children: task.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            className: getTaskStatusColor(task.completion_status),\n                                                            children: formatTaskStatus(task.completion_status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, task.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            dashboardData.recent_tasks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"Tidak ada tugas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full mt-3\",\n                                        onClick: ()=>router.push(\"/project/task?projectId=\".concat(id)),\n                                        children: \"Lihat Semua Tugas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"\".concat(palette.bgCard, \" border \").concat(palette.border, \" shadow-sm\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"p-4 pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CalendarDays_CheckCircle_CheckSquare_Eye_FileText_Plus_Scroll_Target_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#dfb14a]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                dashboardData.weekly_logs_count,\n                                                \" Log Mingguan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"p-4 pt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            dashboardData.recent_weekly_logs.slice(0, 2).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between border-b last:border-none pb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-xs\",\n                                                            children: [\n                                                                \"Minggu #\",\n                                                                log.week_number\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: [\n                                                                log.notes_count,\n                                                                \" catatan\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, log.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            dashboardData.recent_weekly_logs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 text-xs\",\n                                                children: \"Tidak ada log mingguan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full mt-3\",\n                                        onClick: ()=>router.push(\"/project/\".concat(id, \"/weekly-log\")),\n                                        children: \"Lihat Semua Log\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                open: deleteDialogOpen,\n                onOpenChange: setDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogTitle, {\n                                    children: \"Hapus Proyek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogDescription, {\n                                    children: \"Apakah Anda yakin ingin menghapus proyek ini? Tindakan ini tidak dapat dibatalkan.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    disabled: deleteLoading,\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: handleDelete,\n                                    disabled: deleteLoading,\n                                    children: deleteLoading ? 'Menghapus...' : 'Hapus'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                    lineNumber: 553,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n                lineNumber: 552,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectDashboardContent.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDashboardContent, \"8ZU5MIOROIm4/loS7tGVBmfEBL4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useRBAC__WEBPACK_IMPORTED_MODULE_14__.useRBAC,\n        _hooks_useProjectDashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    ];\n});\n_c = ProjectDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDashboardContent.tsx\n"));

/***/ })

});