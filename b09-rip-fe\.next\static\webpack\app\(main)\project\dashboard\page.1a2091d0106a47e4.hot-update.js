"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/project/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx":
/*!*************************************************************!*\
  !*** ./src/components/project/ProjectsDashboardContent.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsDashboardContent: () => (/* binding */ ProjectsDashboardContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chart-no-axes-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart2,CheckSquare,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.479.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/PageTitle */ \"(app-pages-browser)/./src/components/ui/PageTitle.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_utils_date__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/date */ \"(app-pages-browser)/./src/lib/utils/date.ts\");\n/* harmony import */ var _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useProjectsDashboard */ \"(app-pages-browser)/./src/hooks/useProjectsDashboard.ts\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/LabelList.js\");\n/* harmony import */ var _barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Label,LabelList!=!recharts */ \"(app-pages-browser)/./node_modules/.pnpm/recharts@2.15.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/recharts/es6/component/Label.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsDashboardContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProjectsDashboardContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { dashboardData, loading, error, refreshDashboard } = (0,_hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard)();\n    // Chart configurations\n    const statusChartConfig = {\n        not_started: {\n            label: 'Belum Dimulai',\n            theme: {\n                light: '#9CA3AF',\n                dark: '#9CA3AF'\n            },\n            fill: '#9CA3AF'\n        },\n        in_progress: {\n            label: 'Dalam Proses',\n            theme: {\n                light: '#3B82F6',\n                dark: '#3B82F6'\n            },\n            fill: '#3B82F6'\n        },\n        completed: {\n            label: 'Selesai',\n            theme: {\n                light: '#10B981',\n                dark: '#10B981'\n            },\n            fill: '#10B981'\n        },\n        cancelled: {\n            label: 'Dibatalkan',\n            theme: {\n                light: '#EF4444',\n                dark: '#EF4444'\n            },\n            fill: '#EF4444'\n        }\n    };\n    const categoryChartConfig = {\n        category1: {\n            theme: {\n                light: 'hsl(var(--chart-1))',\n                dark: 'hsl(var(--chart-1))'\n            },\n            fill: 'hsl(var(--chart-1))'\n        },\n        category2: {\n            theme: {\n                light: 'hsl(var(--chart-2))',\n                dark: 'hsl(var(--chart-2))'\n            },\n            fill: 'hsl(var(--chart-2))'\n        },\n        category3: {\n            theme: {\n                light: 'hsl(var(--chart-3))',\n                dark: 'hsl(var(--chart-3))'\n            },\n            fill: 'hsl(var(--chart-3))'\n        },\n        category4: {\n            theme: {\n                light: 'hsl(var(--chart-4))',\n                dark: 'hsl(var(--chart-4))'\n            },\n            fill: 'hsl(var(--chart-4))'\n        },\n        category5: {\n            theme: {\n                light: 'hsl(var(--chart-5))',\n                dark: 'hsl(var(--chart-5))'\n            },\n            fill: 'hsl(var(--chart-5))'\n        }\n    };\n    const picChartConfig = {\n        pic: {\n            theme: {\n                light: '#AB8B3B',\n                dark: '#AB8B3B'\n            },\n            fill: '#AB8B3B'\n        },\n        label: {\n            color: 'hsl(var(--background))'\n        }\n    };\n    const kpiChartConfig = {\n        not_started: {\n            theme: {\n                light: 'hsl(var(--chart-1))',\n                dark: 'hsl(var(--chart-1))'\n            },\n            fill: 'hsl(var(--chart-1))'\n        },\n        in_progress: {\n            theme: {\n                light: 'hsl(var(--chart-2))',\n                dark: 'hsl(var(--chart-2))'\n            },\n            fill: 'hsl(var(--chart-2))'\n        },\n        completed_below_target: {\n            theme: {\n                light: 'hsl(var(--chart-3))',\n                dark: 'hsl(var(--chart-3))'\n            },\n            fill: 'hsl(var(--chart-3))'\n        },\n        completed_on_target: {\n            theme: {\n                light: 'hsl(var(--chart-4))',\n                dark: 'hsl(var(--chart-4))'\n            },\n            fill: 'hsl(var(--chart-4))'\n        },\n        completed_above_target: {\n            theme: {\n                light: 'hsl(var(--chart-5))',\n                dark: 'hsl(var(--chart-5))'\n            },\n            fill: 'hsl(var(--chart-5))'\n        }\n    };\n    // Function to get status badge color\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n                return 'bg-gray-200 text-gray-800';\n            case 'in progress':\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-200 text-gray-800';\n        }\n    };\n    // Function to get KPI status color and fill\n    const getKpiStatusColor = function(status) {\n        let forChart = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        switch(status.toLowerCase()){\n            case 'not_started':\n                return forChart ? '#9CA3AF' : 'bg-gray-500';\n            case 'in_progress':\n                return forChart ? '#3B82F6' : 'bg-blue-600';\n            case 'completed_below_target':\n                return forChart ? '#FBBF24' : 'bg-yellow-500';\n            case 'completed_on_target':\n                return forChart ? '#10B981' : 'bg-green-500';\n            case 'completed_above_target':\n                return forChart ? '#059669' : 'bg-emerald-600';\n            default:\n                return forChart ? '#3B82F6' : 'bg-blue-600';\n        }\n    };\n    // Function to get chart fill color for project status\n    const getStatusChartColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'not started':\n            case 'not_started':\n            case 'belum dimulai':\n                return '#9CA3AF'; // Gray-400\n            case 'in progress':\n            case 'in_progress':\n            case 'dalam proses':\n                return '#3B82F6'; // Blue-500\n            case 'completed':\n            case 'selesai':\n                return '#10B981'; // Green-500\n            case 'cancelled':\n            case 'dibatalkan':\n                return '#EF4444'; // Red-500\n            default:\n                return '#9CA3AF'; // Gray-400\n        }\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 w-20 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-32 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                    title: \"Dashboard Proyek\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-red-50 border-red-200 mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Terjadi kesalahan saat memuat data dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-red-600\",\n                                children: error || 'Data tidak tersedia'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: refreshDashboard,\n                                variant: \"outline\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Coba Lagi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare data for charts\n    const statusData = Object.entries(dashboardData.projects.by_status).map((param)=>{\n        let [key, value] = param;\n        const colorKey = key.toLowerCase().replace(/ /g, '_');\n        return {\n            name: key,\n            value,\n            fill: \"var(--color-\".concat(colorKey, \")\")\n        };\n    });\n    // Create an array of colors for categories\n    const categoryColors = [\n        'hsl(var(--chart-1))',\n        'hsl(var(--chart-2))',\n        'hsl(var(--chart-3))',\n        'hsl(var(--chart-4))',\n        'hsl(var(--chart-5))'\n    ];\n    const categoryData = Object.entries(dashboardData.projects.by_category).map((param, index)=>{\n        let [key, value] = param;\n        return {\n            name: key,\n            value,\n            fill: categoryColors[index % categoryColors.length]\n        };\n    });\n    const picData = dashboardData.projects.by_pic.map((pic)=>({\n            name: pic.name,\n            value: pic.count,\n            fill: '#AB8B3B'\n        }));\n    // Prepare data for KPI donut chart\n    const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map((param)=>{\n        let [key, value] = param;\n        return {\n            name: key.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n            value,\n            fill: getKpiStatusColor(key, true)\n        };\n    });\n    // Calculate total KPI achievement for donut chart center\n    const totalKpiAchievement = dashboardData.kpis.achievement_percentage;\n    // Filter upcoming deadlines to only show those within the next month (30 days)\n    const upcomingDeadlinesNextMonth = dashboardData.projects.upcoming_deadlines.filter((project)=>project.days_remaining <= 30);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTitle__WEBPACK_IMPORTED_MODULE_5__.PageTitle, {\n                title: \"Dashboard Proyek\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-gray-600\",\n                children: \"Ringkasan statistik untuk semua proyek.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Proyek\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: dashboardData.projects.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total KPI\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.kpis.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Pencapaian: \",\n                                            dashboardData.kpis.achievement_percentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Tugas\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: dashboardData.tasks.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            \"Selesai: \",\n                                            dashboardData.tasks.completed,\n                                            \" (\",\n                                            Math.round(dashboardData.tasks.completed / dashboardData.tasks.total * 100),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Tenggat Waktu\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: upcomingDeadlinesNextMonth.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Proyek dengan tenggat waktu dalam 30 hari mendatang\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: statusChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                    data: statusData,\n                                                    dataKey: \"value\",\n                                                    nameKey: \"name\",\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    outerRadius: 80,\n                                                    paddingAngle: 2,\n                                                    label: true,\n                                                    children: statusData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: getStatusChartColor(entry.name)\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegend, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartLegendContent, {\n                                                        nameKey: \"name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan Kategori\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: categoryChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: categoryData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 30,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    width: 120\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 42\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    children: categoryData.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: \"hsl(var(--chart-\".concat(index % 5 + 1, \"))\")\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek berdasarkan PIC\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                        config: picChartConfig,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                            data: picData,\n                                            layout: \"vertical\",\n                                            margin: {\n                                                top: 5,\n                                                right: 50,\n                                                left: 20,\n                                                bottom: 5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_20__.CartesianGrid, {\n                                                    horizontal: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_21__.XAxis, {\n                                                    type: \"number\",\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_22__.YAxis, {\n                                                    dataKey: \"name\",\n                                                    type: \"category\",\n                                                    tickLine: false,\n                                                    axisLine: false,\n                                                    hide: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                    cursor: false,\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 30\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_23__.Bar, {\n                                                    dataKey: \"value\",\n                                                    radius: 4,\n                                                    fill: \"#AB8B3B\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"name\",\n                                                            position: \"insideLeft\",\n                                                            offset: 8,\n                                                            className: \"fill-[--color-label]\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_24__.LabelList, {\n                                                            dataKey: \"value\",\n                                                            position: \"right\",\n                                                            offset: 8,\n                                                            className: \"fill-foreground\",\n                                                            fontSize: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Status KPI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"flex-1 pb-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartContainer, {\n                                    config: kpiChartConfig,\n                                    className: \"mx-auto aspect-square max-h-[250px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_16__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltip, {\n                                                cursor: false,\n                                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_9__.ChartTooltipContent, {\n                                                    hideLabel: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 28\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_17__.Pie, {\n                                                data: kpiStatusData,\n                                                dataKey: \"value\",\n                                                nameKey: \"name\",\n                                                innerRadius: 60,\n                                                outerRadius: 80,\n                                                paddingAngle: 2,\n                                                children: [\n                                                    kpiStatusData.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_18__.Cell, {\n                                                            fill: entry.fill\n                                                        }, \"cell-\".concat(entry.name), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Label_LabelList_recharts__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                        content: (param)=>{\n                                                            let { viewBox } = param;\n                                                            if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                    x: viewBox.cx,\n                                                                    y: viewBox.cy,\n                                                                    textAnchor: \"middle\",\n                                                                    dominantBaseline: \"middle\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: viewBox.cy,\n                                                                            className: \"fill-foreground text-3xl font-bold\",\n                                                                            children: [\n                                                                                totalKpiAchievement,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tspan\", {\n                                                                            x: viewBox.cx,\n                                                                            y: (viewBox.cy || 0) + 24,\n                                                                            className: \"fill-muted-foreground text-sm\",\n                                                                            children: \"Pencapaian\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                            lineNumber: 544,\n                                                                            columnNumber: 29\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 27\n                                                                }, void 0);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-col gap-2 text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 font-medium leading-none justify-center\",\n                                        children: dashboardData.kpis.achievement_percentage >= 100 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian melebihi target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : dashboardData.kpis.achievement_percentage >= 80 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian mendekati target\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Pencapaian perlu ditingkatkan\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart2_CheckSquare_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Proyek Terbaru\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: dashboardData.projects.recent.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: project.project_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: project.organization_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: getStatusColor(project.status_project),\n                                                            children: project.status_project\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"PIC: \",\n                                                                project.pic_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: project.days_remaining > 0 ? \"\".concat(project.days_remaining, \" hari tersisa\") : 'Tenggat waktu terlewati'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Tenggat Waktu Mendatang (30 Hari)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upcomingDeadlinesNextMonth.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: project.project_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: project.days_remaining <= 7 ? 'border-red-500 text-red-700' : project.days_remaining <= 30 ? 'border-yellow-500 text-yellow-700' : 'border-green-500 text-green-700',\n                                                            children: [\n                                                                project.days_remaining,\n                                                                \" hari\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between mt-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Tenggat: \",\n                                                                (0,_lib_utils_date__WEBPACK_IMPORTED_MODULE_7__.formatDate)(project.end_project)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: [\n                                                                \"Progres: \",\n                                                                project.progress_percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"link\",\n                                                    className: \"px-0 h-auto text-sm mt-1\",\n                                                    onClick: ()=>router.push(\"/project/\".concat(project.id)),\n                                                    children: \"Lihat Detail\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, project.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\UAT Propen\\\\b09-rip-fe\\\\src\\\\components\\\\project\\\\ProjectsDashboardContent.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsDashboardContent, \"dYLgM1N2lllAS4iOBb0m1Po550g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_useProjectsDashboard__WEBPACK_IMPORTED_MODULE_8__.useProjectsDashboard\n    ];\n});\n_c = ProjectsDashboardContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectsDashboardContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectsDashboardContent.tsx\n"));

/***/ })

});