'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import {
  BarChart2,
  Calendar,
  CheckCircle,
  FileText,
  Target,
  AlertCircle,
  Plus,
  CheckSquare,
  CalendarDays,
  Eye,
  Scroll,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/utils/format';
import { formatDate } from '@/lib/utils/date';
import useProjectDashboard from '@/hooks/useProjectDashboard';
import { Project } from '@/types/project';
import { projectApi } from '@/lib/api/project';
import { useRB<PERSON> } from '@/hooks/useRBAC';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';

// --- Helper: Gold/White palette
const palette = {
  bgMain: 'bg-[#fffbea]',      // light gold
  bgCard: 'bg-white',
  gold: 'text-[#dfb14a]',
  goldBg: 'bg-[#fff2ce]',
  goldAccent: 'bg-[#f7e3a1]',
  border: 'border-[#ecd9a0]',
  progress: 'bg-gradient-to-r from-[#ffe18f] to-[#dfb14a]',
  green: 'text-emerald-700 bg-emerald-100',
  blue: 'text-blue-700 bg-blue-100',
  red: 'text-red-700 bg-red-100',
  gray: 'text-gray-700 bg-gray-100',
};

interface ProjectDashboardContentProps {
  id: string;
}

export function ProjectDashboardContent({ id }: ProjectDashboardContentProps) {
  const router = useRouter();
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Operation', 'Manager']);
  const canDelete = hasRole(['Manager']);
  const { dashboardData, loading, error, refreshDashboard } = useProjectDashboard(id);
  const [project, setProject] = useState<Project | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Fetch full project data to get project_charter_id
  const fetchProject = useCallback(async () => {
    if (!id) return;
    try {
      const response = await projectApi.getProjectById(id);
      if (response.success && response.data) {
        setProject(response.data);
      } else {
        console.error(`Failed to fetch project: ${response.message}`);
      }
    } catch (error) {
      console.error('Error fetching project:', error);
    }
  }, [id]);

  useEffect(() => {
    fetchProject();
  }, [fetchProject]);

  // Handle project deletion
  const handleDelete = async () => {
    if (!canDelete) {
      toast.error('Anda tidak memiliki izin untuk menghapus proyek');
      setDeleteDialogOpen(false);
      return;
    }
    try {
      setDeleteLoading(true);
      const response = await projectApi.deleteProject(id);
      if (response.success) {
        toast.success('Proyek berhasil dihapus');
        router.push('/project');
      } else {
        toast.error(`Gagal menghapus proyek: ${response.message}`);
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      toast.error('Terjadi kesalahan saat menghapus proyek');
    } finally {
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
    }
  };

  // --- Status helpers
  const statusColors = {
    'Not Started': palette.gray,
    'In Progress': palette.blue,
    'Completed': palette.green,
    'Cancelled': palette.red,
  };
  const getStatusColor = (status: string) => statusColors[status] || palette.gray;

  const kpiColors = {
    'not_started': palette.gray,
    'in_progress': palette.blue,
    'completed_below_target': 'bg-yellow-100 text-yellow-800',
    'completed_on_target': palette.green,
    'completed_above_target': 'bg-[#e7f8e5] text-[#107c41]',
  };
  const getKpiStatusColor = (status: string) => kpiColors[status] || palette.gray;

  const taskColors = {
    'not_completed': palette.red,
    'on_progress': palette.blue,
    'completed': palette.green,
  };
  const getTaskStatusColor = (status: string) => taskColors[status] || palette.gray;

  // Format status
  const formatKpiStatus = (status: string) => ({
    'not_started': 'Belum Dimulai',
    'in_progress': 'Dalam Proses',
    'completed_below_target': 'Selesai Di Bawah Target',
    'completed_on_target': 'Selesai Sesuai Target',
    'completed_above_target': 'Selesai Di Atas Target',
  }[status] || status);

  const formatTaskStatus = (status: string) => ({
    'not_completed': 'Belum Selesai',
    'on_progress': 'Dalam Proses',
    'completed': 'Selesai',
  }[status] || status);

  // Calculate task completion percent
  let taskPercent = 0;
  let kpiPercent = 0;
  if (dashboardData && dashboardData.tasks_total > 0) {
    taskPercent = Math.round((dashboardData.tasks_completed / dashboardData.tasks_total) * 100);
  }
  if (dashboardData && dashboardData.kpi_count > 0 && dashboardData.kpis) {
    const completedKpis = dashboardData.kpis.filter(
      (kpi) => kpi.status === 'completed_on_target' || kpi.status === 'completed_above_target'
    ).length;
    kpiPercent = Math.round((completedKpis / dashboardData.kpi_count) * 100);
  }

  // --- Loading state
  if (loading) {
    return (
      <div className={`${palette.bgMain} min-h-screen py-4 px-2`}>
        <div className="flex items-center gap-4 mb-4">
          <BackButton onClick={() => router.push(`/project`)} />
          <PageTitle title="Dashboard Proyek" />
        </div>
        <div className="grid gap-4 lg:grid-cols-3 md:grid-cols-2">
          <Skeleton className="h-40 w-full col-span-2" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-56 w-full" />
          <Skeleton className="h-56 w-full" />
          <Skeleton className="h-56 w-full" />
        </div>
      </div>
    );
  }

  // --- Error state
  if (error || !dashboardData) {
    return (
      <div className={`${palette.bgMain} min-h-screen py-4 px-2`}>
        <div className="flex items-center gap-4 mb-4">
          <BackButton onClick={() => router.push('/project')} />
          <PageTitle title="Dashboard Proyek" />
        </div>
        <Card className={`border ${palette.border} ${palette.goldBg}`}>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <p>Terjadi kesalahan saat memuat data dashboard.</p>
            </div>
            <p className="mt-2 text-sm text-red-600">
              {error || 'Data tidak tersedia'}
            </p>
            <Button
              onClick={refreshDashboard}
              variant="outline"
              className="mt-4"
            >
              Coba Lagi
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // --- Main dashboard
  return (
    <div className={`${palette.bgMain} min-h-screen py-4 px-2`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push(`/project`)} />
          <PageTitle title="Dashboard Proyek" />
        </div>
        <div className="flex gap-2 flex-wrap">
          {project && (
            <>
              {canEdit &&
                (!project.project_charter_id ||
                  project.project_charter_id ===
                    'TODO-project-charter-implementation') && (
                  <Button
                    onClick={() => router.push(`/project/${project.id}/charter/create`)}
                    className={`flex items-center gap-2 ${palette.goldBg} border ${palette.border} ${palette.gold}`}
                  >
                    <Plus className="h-4 w-4" />
                    Tambah Project Charter
                  </Button>
                )}
              {project.project_charter_id &&
                project.project_charter_id !==
                  'TODO-project-charter-implementation' && (
                  <Button
                    onClick={() => router.push(`/project/${project.id}/charter`)}
                    variant="outline"
                    className={`flex items-center gap-2 ${palette.gold}`}
                  >
                    <Scroll className="h-4 w-4" />
                    Project Charter
                  </Button>
                )}
            </>
          )}
          <Button variant="outline" className="flex items-center gap-2" onClick={() => router.push(`/project/${id}/detail`)}>
            <Eye className="h-4 w-4" />
            Detail Proyek
          </Button>
          <Button variant="outline" className="flex items-center gap-2" onClick={() => router.push(`/project/${id}/gantt`)}>
            <BarChart2 className="h-4 w-4" />
            Gantt Chart
          </Button>
          <Button variant="outline" className="flex items-center gap-2" onClick={() => router.push(`/project/${id}/weekly-log`)}>
            <CalendarDays className="h-4 w-4" />
            Weekly Log
          </Button>
          <Button variant="outline" className="flex items-center gap-2" onClick={() => router.push(`/kpi-project/with-details?projectId=${id}`)}>
            <Target className="h-4 w-4" />
            Lihat KPI
          </Button>
          <Button variant="outline" className="flex items-center gap-2" onClick={() => router.push(`/project/task?projectId=${id}`)}>
            <CheckSquare className="h-4 w-4" />
            Tugas Proyek
          </Button>
          {canDelete && (
            <Button variant="destructive" onClick={() => setDeleteDialogOpen(true)}>
              Hapus
            </Button>
          )}
        </div>
      </div>

      {/* Main Header: Project Info and Progress */}
      <div className="grid lg:grid-cols-3 gap-4 mb-4">
        {/* Project Info Card */}
        <Card className={`${palette.bgCard} border ${palette.border} shadow-sm`}>
          <CardHeader className="p-4 pb-2">
            <div className="flex flex-col gap-1">
              <h2 className="text-xl font-bold">{dashboardData.project_name}</h2>
              <p className={`${palette.gold} text-base`}>{dashboardData.organization_name}</p>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-2">
            <div className="flex flex-col gap-1">
              <div className="flex gap-1">
                <Badge className={getStatusColor(dashboardData.status_project)}>
                  {dashboardData.status_project}
                </Badge>
                <Badge variant="outline">{dashboardData.project_category}</Badge>
              </div>
              <div className="flex items-center gap-2 mt-2">
                <span className="font-medium text-gray-700">Tujuan:</span>
                <span className="text-gray-500 text-sm">{dashboardData.objectives}</span>
              </div>
              <div className="flex items-center gap-2 mt-2">
                <span className="font-medium text-gray-700">Anggaran:</span>
                <span className="text-xl font-semibold">{formatCurrency(parseInt(dashboardData.budget_project))}</span>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Progress Card */}
        <Card className={`col-span-2 ${palette.bgCard} border ${palette.border} shadow-sm`}>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row items-center gap-6">
              {/* Project Progress */}
              <div className="flex-1 flex flex-col items-center">
                <span className="text-sm text-gray-500 mb-1">Progres Proyek</span>
                <div className="relative flex items-center justify-center h-24 w-24">
                  {/* Circular progress - SVG */}
                  <svg className="absolute" width="96" height="96">
                    <circle
                      cx="48" cy="48" r="40"
                      stroke="#f7e3a1"
                      strokeWidth="12"
                      fill="none"
                    />
                    <circle
                      cx="48" cy="48" r="40"
                      stroke="#dfb14a"
                      strokeWidth="12"
                      fill="none"
                      strokeDasharray={2 * Math.PI * 40}
                      strokeDashoffset={2 * Math.PI * 40 * (1 - dashboardData.progress_percentage / 100)}
                      strokeLinecap="round"
                      style={{ transition: 'stroke-dashoffset 0.5s' }}
                    />
                  </svg>
                  <span className={`${palette.gold} absolute font-bold text-2xl`}>
                    {dashboardData.progress_percentage}%
                  </span>
                </div>
                <div className="flex gap-2 text-xs mt-2">
                  <div className="flex flex-col items-center">
                    <span className="text-gray-500">Hari Berlalu</span>
                    <span className="font-bold">{dashboardData.days_elapsed}</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <span className="text-gray-500">Tersisa</span>
                    <span className="font-bold">{dashboardData.days_remaining}</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <span className="text-gray-500">Total</span>
                    <span className="font-bold">{dashboardData.days_total}</span>
                  </div>
                </div>
              </div>
              {/* Timeline */}
              <div className="flex-1 flex flex-col items-center">
                <span className="text-sm text-gray-500 mb-1">Timeline</span>
                <div className="flex gap-6 mb-2 w-full justify-between">
                  <div>
                    <span className="text-xs text-gray-500">Mulai</span>
                    <div className="text-sm font-medium">{formatDate(dashboardData.start_project)}</div>
                  </div>
                  <div className="text-right">
                    <span className="text-xs text-gray-500">Selesai</span>
                    <div className="text-sm font-medium">{formatDate(dashboardData.end_project)}</div>
                  </div>
                </div>
                <div className="w-full h-3 relative">
                  <div className="absolute w-full h-3 rounded-full" style={{ background: '#fff2ce' }} />
                  <div className="absolute h-3 rounded-full" style={{
                    background: 'linear-gradient(90deg,#ffe18f,#dfb14a)',
                    width: `${(dashboardData.days_elapsed / dashboardData.days_total) * 100}%`,
                  }} />
                  <div
                    className="absolute top-0 w-4 h-4 rounded-full border-2 border-white"
                    style={{
                      background: '#dfb14a',
                      left: `${(dashboardData.days_elapsed / dashboardData.days_total) * 100}%`,
                      transform: 'translate(-50%,-25%)',
                    }}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Summary Cards: KPI, Task, Weekly Log */}
      <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-4">
        {/* KPI Card */}
        <Card className={`${palette.bgCard} border ${palette.border} shadow-sm`}>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-[#dfb14a]" />
              <span>{dashboardData.kpi_count} KPI</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-2">
            <div className="flex flex-col items-center gap-2 mb-2">
              {/* KPI Circular Progress */}
              <div className="relative flex items-center justify-center h-16 w-16">
                <svg className="absolute" width="64" height="64">
                  <circle
                    cx="32" cy="32" r="26"
                    stroke="#f7e3a1"
                    strokeWidth="8"
                    fill="none"
                  />
                  <circle
                    cx="32" cy="32" r="26"
                    stroke="#dfb14a"
                    strokeWidth="8"
                    fill="none"
                    strokeDasharray={2 * Math.PI * 26}
                    strokeDashoffset={2 * Math.PI * 26 * (1 - kpiPercent / 100)}
                    strokeLinecap="round"
                  />
                </svg>
                <span className="absolute text-lg font-bold text-[#dfb14a]">{kpiPercent}%</span>
              </div>
              <span className="text-xs text-gray-600">
                KPI Tercapai ({kpiPercent}%)
              </span>
            </div>
            <div className="mb-3 flex flex-col gap-1">
              <Badge className={getKpiStatusColor(dashboardData.kpi_status)}>
                {formatKpiStatus(dashboardData.kpi_status)}
              </Badge>
            </div>
            <div className="space-y-2">
              {dashboardData.kpis.slice(0, 2).map((kpi) => (
                <div key={kpi.id} className="flex items-center justify-between border-b last:border-none pb-1">
                  <div>
                    <span className="font-medium text-xs">{kpi.description}</span>
                  </div>
                  <Badge className={getKpiStatusColor(kpi.status)}>
                    {formatKpiStatus(kpi.status)}
                  </Badge>
                </div>
              ))}
              {dashboardData.kpis.length === 0 && (
                <span className="text-gray-400 text-xs">Tidak ada KPI</span>
              )}
            </div>
            <Button
              variant="outline"
              className="w-full mt-3"
              onClick={() => router.push(`/kpi-project/with-details?projectId=${id}`)}
            >
              Lihat Semua KPI
            </Button>
          </CardContent>
        </Card>

        {/* Task Card */}
        <Card className={`${palette.bgCard} border ${palette.border} shadow-sm`}>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-[#dfb14a]" />
              <span>{dashboardData.tasks_total} Tugas</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-2">
            <div className="flex flex-col items-center gap-2 mb-2">
              {/* Task Completion Progress Bar */}
              <div className="w-full h-4 rounded-full bg-[#fff2ce] relative">
                <div
                  className="h-4 rounded-full"
                  style={{
                    width: `${taskPercent}%`,
                    background: 'linear-gradient(90deg,#ffe18f,#dfb14a)',
                  }}
                />
                <span className="absolute right-2 top-0.5 text-xs font-bold text-[#dfb14a]">{taskPercent}%</span>
              </div>
              <span className="text-xs text-gray-600">
                Penyelesaian Tugas ({taskPercent}%)
              </span>
            </div>
            <div className="flex justify-between text-xs mt-2 mb-2">
              <div>
                <span className="font-bold text-emerald-700">{dashboardData.tasks_completed}</span>
                <span className="ml-1 text-gray-500">Selesai</span>
              </div>
              <div>
                <span className="font-bold text-blue-700">{dashboardData.tasks_in_progress}</span>
                <span className="ml-1 text-gray-500">Proses</span>
              </div>
              <div>
                <span className="font-bold text-red-700">{dashboardData.tasks_not_started}</span>
                <span className="ml-1 text-gray-500">Belum</span>
              </div>
            </div>
            <div className="space-y-2">
              {dashboardData.recent_tasks.slice(0, 2).map((task) => (
                <div key={task.id} className="flex items-center justify-between border-b last:border-none pb-1">
                  <span className="text-xs font-medium">{task.description}</span>
                  <Badge className={getTaskStatusColor(task.completion_status)}>
                    {formatTaskStatus(task.completion_status)}
                  </Badge>
                </div>
              ))}
              {dashboardData.recent_tasks.length === 0 && (
                <span className="text-gray-400 text-xs">Tidak ada tugas</span>
              )}
            </div>
            <Button
              variant="outline"
              className="w-full mt-3"
              onClick={() => router.push(`/project/task?projectId=${id}`)}
            >
              Lihat Semua Tugas
            </Button>
          </CardContent>
        </Card>

        {/* Weekly Log Card */}
        <Card className={`${palette.bgCard} border ${palette.border} shadow-sm`}>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-[#dfb14a]" />
              <span>{dashboardData.weekly_logs_count} Log Mingguan</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-2">
            <div className="space-y-2">
              {dashboardData.recent_weekly_logs.slice(0, 2).map((log) => (
                <div key={log.id} className="flex items-center justify-between border-b last:border-none pb-1">
                  <span className="font-medium text-xs">Minggu #{log.week_number}</span>
                  <Badge variant="outline" className="text-xs">
                    {log.notes_count} catatan
                  </Badge>
                </div>
              ))}
              {dashboardData.recent_weekly_logs.length === 0 && (
                <span className="text-gray-400 text-xs">Tidak ada log mingguan</span>
              )}
            </div>
            <Button
              variant="outline"
              className="w-full mt-3"
              onClick={() => router.push(`/project/${id}/weekly-log`)}
            >
              Lihat Semua Log
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Proyek</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus proyek ini? Tindakan ini tidak
              dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={deleteLoading}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteLoading}
            >
              {deleteLoading ? 'Menghapus...' : 'Hapus'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
